import { concatBytes } from '../../static_dependencies/noble-curves/abstract/utils.js';
declare const json: (data: any, params?: any) => string, isJsonEncodedObject: (object: any) => boolean, binaryToString: (data: Uint8Array) => string, stringToBinary: (str: string) => Uint8Array, stringToBase64: (string: string) => string, base64ToString: (string: string) => string, base64ToBinary: (str: string) => Uint8Array, binaryToBase64: (data: Uint8Array) => string, base16ToBinary: (str: string) => Uint8Array, binaryToBase16: (data: Uint8Array) => string, base58ToBinary: (str: string) => Uint8Array, binaryToBase58: (data: Uint8Array) => string, binaryConcat: typeof concatBytes, binaryConcatArray: (arr: any[]) => Uint8Array, urlencode: (object: object, sort?: boolean) => string, urlencodeNested: (object: object) => string, urlencodeWithArrayRepeat: (object: object) => string, rawencode: (object: object, sort?: boolean) => string, encode: (str: string) => Uint8Array, decode: (data: Uint8Array) => string, urlencodeBase64: (payload: string | Uint8Array) => string, numberToLE: (n: number, padding: number) => Uint8Array, numberToBE: (n: number, padding: number) => Uint8Array;
declare function packb(req: any): Uint8Array;
export { json, isJsonEncodedObject, binaryToString, stringToBinary, stringToBase64, base64ToBinary, base64ToString, binaryToBase64, base16ToBinary, binaryToBase16, binaryConcat, binaryConcatArray, urlencode, urlencodeWithArrayRepeat, rawencode, encode, decode, urlencodeBase64, numberToLE, numberToBE, base58ToBinary, binaryToBase58, urlencodeNested, packb };
