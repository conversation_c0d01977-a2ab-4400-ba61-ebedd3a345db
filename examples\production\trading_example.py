#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
CCXT 生产级交易示例 (Python)
"""

import asyncio
import ccxt
import os
import sys
from typing import Dict, Optional, Any

# 加载配置
try:
    from config import EXCHANGES, COMMON_CONFIG
except ImportError:
    print("⚠️  配置文件不存在，使用示例配置")
    print("请复制 config.example.py 为 config.py 并填入您的 API 密钥")
    
    # 使用示例配置
    EXCHANGES = {
        'binance': {
            'apiKey': 'demo_key',
            'secret': 'demo_secret',
            'sandbox': True,
            'enableRateLimit': True,
        }
    }
    COMMON_CONFIG = {
        'timeout': 30000,
        'verbose': False,
        'enableRateLimit': True,
    }

class TradingBot:
    def __init__(self, exchange_name: str, exchange_config: Dict[str, Any]):
        self.exchange_name = exchange_name
        self.exchange_config = {**COMMON_CONFIG, **exchange_config}
        self.exchange = getattr(ccxt, exchange_name)(self.exchange_config)
        self.is_connected = False

    async def initialize(self):
        """初始化交易机器人"""
        try:
            print(f"🔄 初始化 {self.exchange_name} 交易所...")
            
            # 检查交易所连接
            await self.test_connection()
            
            # 加载市场数据
            await self.load_markets()
            
            self.is_connected = True
            print(f"✅ {self.exchange_name} 初始化成功")
            
        except Exception as error:
            print(f"❌ {self.exchange_name} 初始化失败: {error}")
            raise

    async def test_connection(self):
        """测试交易所连接"""
        try:
            # 测试公共 API
            ticker = await self.exchange.fetch_ticker('BTC/USDT')
            print(f"📊 BTC/USDT 价格: ${ticker['last']}")
            
            # 如果有 API 密钥，测试私有 API
            if self.exchange.apiKey and self.exchange.apiKey != 'demo_key':
                balance = await self.exchange.fetch_balance()
                print("💰 账户余额获取成功")
            else:
                print("⚠️  使用演示模式（无 API 密钥）")
                
        except ccxt.AuthenticationError:
            print("🔑 认证失败，请检查 API 密钥")
            raise
        except ccxt.NetworkError:
            print("🌐 网络错误，请检查网络连接")
            raise

    async def load_markets(self):
        """加载市场数据"""
        print("📈 加载市场数据...")
        markets = await self.exchange.load_markets()
        print(f"✅ 已加载 {len(markets)} 个交易对")
        
        # 显示一些热门交易对
        popular_pairs = ['BTC/USDT', 'ETH/USDT', 'BNB/USDT', 'ADA/USDT', 'SOL/USDT']
        print("🔥 热门交易对:")
        
        for symbol in popular_pairs:
            if symbol in markets:
                status = "✅" if markets[symbol]['active'] else "❌"
                print(f"   {symbol} - {status}")

    async def fetch_market_data(self, symbol: str = 'BTC/USDT'):
        """获取市场数据"""
        try:
            print(f"\n📊 获取 {symbol} 市场数据...")
            
            # 获取行情数据
            ticker = await self.exchange.fetch_ticker(symbol)
            print(f"💰 当前价格: ${ticker['last']}")
            if ticker['percentage']:
                print(f"📈 24h 涨跌: {ticker['percentage']:.2f}%")
            if ticker['baseVolume']:
                print(f"📊 24h 成交量: {ticker['baseVolume']:.2f}")
            
            # 获取订单簿
            orderbook = await self.exchange.fetch_order_book(symbol, 5)
            print(f"\n📋 订单簿 (前5档):")
            print("买盘:")
            for i, bid in enumerate(orderbook['bids'][:3]):
                print(f"   {i + 1}. ${bid[0]} - {bid[1]}")
            print("卖盘:")
            for i, ask in enumerate(orderbook['asks'][:3]):
                print(f"   {i + 1}. ${ask[0]} - {ask[1]}")
            
            return {'ticker': ticker, 'orderbook': orderbook}
            
        except Exception as error:
            print(f"❌ 获取市场数据失败: {error}")
            raise

    async def fetch_account_info(self):
        """获取账户信息"""
        try:
            if not self.exchange.apiKey or self.exchange.apiKey == 'demo_key':
                print("⚠️  需要 API 密钥才能获取账户信息")
                return None

            print("\n👤 获取账户信息...")
            
            balance = await self.exchange.fetch_balance()
            print("💰 账户余额:")
            
            # 显示非零余额
            for currency, amount in balance['total'].items():
                if amount > 0:
                    print(f"   {currency}: {amount}")
            
            return balance
            
        except Exception as error:
            print(f"❌ 获取账户信息失败: {error}")
            raise

    async def demonstrate_order_operations(self, symbol: str = 'BTC/USDT'):
        """演示订单操作"""
        try:
            if not self.exchange.apiKey or self.exchange.apiKey == 'demo_key':
                print("\n⚠️  演示模式：订单操作需要真实 API 密钥")
                print("📝 订单操作示例代码:")
                print("   # 创建限价买单")
                print("   order = await exchange.create_limit_buy_order('BTC/USDT', 0.001, 50000)")
                print("   # 创建限价卖单")
                print("   order = await exchange.create_limit_sell_order('BTC/USDT', 0.001, 60000)")
                print("   # 取消订单")
                print("   await exchange.cancel_order(order['id'], 'BTC/USDT')")
                return

            print(f"\n📝 订单操作演示 ({symbol})...")
            print("⚠️  注意：这是真实交易环境，请谨慎操作！")
            
            # 获取当前价格
            ticker = await self.exchange.fetch_ticker(symbol)
            current_price = ticker['last']
            
            # 演示创建订单（但不实际执行）
            print("💡 订单示例（未实际执行）:")
            print(f"   限价买单: 价格 ${current_price * 0.95:.2f}, 数量 0.001")
            print(f"   限价卖单: 价格 ${current_price * 1.05:.2f}, 数量 0.001")
            
            # 获取历史订单
            orders = await self.exchange.fetch_orders(symbol, limit=10)
            print(f"📋 最近 {len(orders)} 个订单:")
            for order in orders[:5]:
                print(f"   {order['side']} {order['amount']} {order['symbol']} @ ${order['price']} - {order['status']}")
            
        except Exception as error:
            print(f"❌ 订单操作演示失败: {error}")

async def main():
    """主程序"""
    print("🚀 CCXT 生产级交易示例启动\n")
    
    try:
        # 选择交易所
        exchange_name = 'binance'
        exchange_config = EXCHANGES.get(exchange_name)
        
        if not exchange_config:
            raise ValueError(f"未找到 {exchange_name} 的配置")
        
        # 创建交易机器人
        bot = TradingBot(exchange_name, exchange_config)
        
        # 初始化
        await bot.initialize()
        
        # 获取市场数据
        await bot.fetch_market_data('BTC/USDT')
        await bot.fetch_market_data('ETH/USDT')
        
        # 获取账户信息
        await bot.fetch_account_info()
        
        # 演示订单操作
        await bot.demonstrate_order_operations('BTC/USDT')
        
        print("\n✅ 示例执行完成")
        print("\n📚 下一步建议:")
        print("1. 配置真实的 API 密钥")
        print("2. 在沙盒环境中测试")
        print("3. 实现风险管理策略")
        print("4. 添加日志和监控")
        print("5. 阅读交易所 API 文档")
        
    except Exception as error:
        print(f"❌ 程序执行失败: {error}")
        sys.exit(1)

if __name__ == "__main__":
    # 运行异步主程序
    asyncio.run(main())
