# CCXT API 配置示例文件 (Python)
# 复制此文件为 config.py 并填入您的真实 API 密钥

import os

# 从环境变量读取 API 密钥（推荐方式）
def get_env_var(name, default=''):
    return os.environ.get(name, default)

# 交易所配置
EXCHANGES = {
    # Binance 配置
    'binance': {
        'apiKey': get_env_var('BINANCE_API_KEY', 'your_binance_api_key_here'),
        'secret': get_env_var('BINANCE_SECRET', 'your_binance_secret_here'),
        'sandbox': True,  # 设置为 False 用于实盘交易
        'enableRateLimit': True,
        'options': {
            'defaultType': 'spot',  # 'spot', 'margin', 'future', 'delivery'
        }
    },

    # Coinbase Pro 配置
    'coinbasepro': {
        'apiKey': get_env_var('COINBASE_API_KEY', 'your_coinbase_api_key_here'),
        'secret': get_env_var('COINBASE_SECRET', 'your_coinbase_secret_here'),
        'password': get_env_var('COINBASE_PASSPHRASE', 'your_coinbase_passphrase_here'),
        'sandbox': True,
        'enableRateLimit': True,
    },

    # OKX 配置
    'okx': {
        'apiKey': get_env_var('OKX_API_KEY', 'your_okx_api_key_here'),
        'secret': get_env_var('OKX_SECRET', 'your_okx_secret_here'),
        'password': get_env_var('OKX_PASSPHRASE', 'your_okx_passphrase_here'),
        'sandbox': True,
        'enableRateLimit': True,
    },

    # Kraken 配置
    'kraken': {
        'apiKey': get_env_var('KRAKEN_API_KEY', 'your_kraken_api_key_here'),
        'secret': get_env_var('KRAKEN_SECRET', 'your_kraken_secret_here'),
        'enableRateLimit': True,
    },

    # Huobi 配置
    'huobi': {
        'apiKey': get_env_var('HUOBI_API_KEY', 'your_huobi_api_key_here'),
        'secret': get_env_var('HUOBI_SECRET', 'your_huobi_secret_here'),
        'enableRateLimit': True,
    },
}

# 通用配置
COMMON_CONFIG = {
    'timeout': 30000,  # 30秒超时
    'verbose': False,  # 设置为 True 启用详细日志
    'enableRateLimit': True,
    'rateLimit': 1000,  # 毫秒
}

# 环境变量设置示例（在 .env 文件中）
"""
BINANCE_API_KEY=your_actual_binance_api_key
BINANCE_SECRET=your_actual_binance_secret
COINBASE_API_KEY=your_actual_coinbase_api_key
COINBASE_SECRET=your_actual_coinbase_secret
COINBASE_PASSPHRASE=your_actual_coinbase_passphrase
"""

# 安全提示：
# 1. 永远不要将真实的 API 密钥提交到版本控制系统
# 2. 使用环境变量存储敏感信息
# 3. 在生产环境中设置 sandbox=False
# 4. 定期轮换 API 密钥
# 5. 为 API 密钥设置适当的权限（只读、交易等）
