# -*- coding: utf-8 -*-

"""
CCXT Error Classes
"""

class BaseError(Exception):
    """Base exception class for CCXT"""
    pass

class ExchangeError(BaseError):
    """Exchange-specific error"""
    pass

class NetworkError(BaseError):
    """Network-related error"""
    pass

class RequestTimeout(NetworkError):
    """Request timeout error"""
    pass

class ExchangeNotAvailable(NetworkError):
    """Exchange not available error"""
    pass

class AuthenticationError(ExchangeError):
    """Authentication error"""
    pass

class PermissionDenied(AuthenticationError):
    """Permission denied error"""
    pass

class InsufficientFunds(ExchangeError):
    """Insufficient funds error"""
    pass

class InvalidOrder(ExchangeError):
    """Invalid order error"""
    pass

class OrderNotFound(ExchangeError):
    """Order not found error"""
    pass

class NotSupported(ExchangeError):
    """Feature not supported error"""
    pass

class BadSymbol(ExchangeError):
    """Bad symbol error"""
    pass

class BadRequest(ExchangeError):
    """Bad request error"""
    pass

class RateLimitExceeded(NetworkError):
    """Rate limit exceeded error"""
    pass
