#!/usr/bin/env node

// CCXT 生产级交易示例 (JavaScript)
const ccxt = require('ccxt');
const fs = require('fs');
const path = require('path');

// 加载配置
let config;
try {
    // 尝试加载配置文件
    config = require('../../config.js');
} catch (error) {
    console.log('⚠️  配置文件不存在，使用示例配置');
    console.log('请复制 config.example.js 为 config.js 并填入您的 API 密钥');
    
    // 使用示例配置
    config = {
        binance: {
            apiKey: 'demo_key',
            secret: 'demo_secret',
            sandbox: true,
            enableRateLimit: true,
        }
    };
}

class TradingBot {
    constructor(exchangeName, exchangeConfig) {
        this.exchangeName = exchangeName;
        this.exchange = new ccxt[exchangeName](exchangeConfig);
        this.isConnected = false;
    }

    async initialize() {
        try {
            console.log(`🔄 初始化 ${this.exchangeName} 交易所...`);
            
            // 检查交易所连接
            await this.testConnection();
            
            // 加载市场数据
            await this.loadMarkets();
            
            this.isConnected = true;
            console.log(`✅ ${this.exchangeName} 初始化成功`);
            
        } catch (error) {
            console.error(`❌ ${this.exchangeName} 初始化失败:`, error.message);
            throw error;
        }
    }

    async testConnection() {
        try {
            // 测试公共 API
            const ticker = await this.exchange.fetchTicker('BTC/USDT');
            console.log(`📊 BTC/USDT 价格: $${ticker.last}`);
            
            // 如果有 API 密钥，测试私有 API
            if (this.exchange.apiKey && this.exchange.apiKey !== 'demo_key') {
                const balance = await this.exchange.fetchBalance();
                console.log(`💰 账户余额获取成功`);
            } else {
                console.log(`⚠️  使用演示模式（无 API 密钥）`);
            }
            
        } catch (error) {
            if (error instanceof ccxt.AuthenticationError) {
                console.log(`🔑 认证失败，请检查 API 密钥`);
            } else if (error instanceof ccxt.NetworkError) {
                console.log(`🌐 网络错误，请检查网络连接`);
            }
            throw error;
        }
    }

    async loadMarkets() {
        console.log(`📈 加载市场数据...`);
        const markets = await this.exchange.loadMarkets();
        console.log(`✅ 已加载 ${Object.keys(markets).length} 个交易对`);
        
        // 显示一些热门交易对
        const popularPairs = ['BTC/USDT', 'ETH/USDT', 'BNB/USDT', 'ADA/USDT', 'SOL/USDT'];
        console.log(`🔥 热门交易对:`);
        
        for (const symbol of popularPairs) {
            if (markets[symbol]) {
                console.log(`   ${symbol} - ${markets[symbol].active ? '✅' : '❌'}`);
            }
        }
    }

    async fetchMarketData(symbol = 'BTC/USDT') {
        try {
            console.log(`\n📊 获取 ${symbol} 市场数据...`);
            
            // 获取行情数据
            const ticker = await this.exchange.fetchTicker(symbol);
            console.log(`💰 当前价格: $${ticker.last}`);
            console.log(`📈 24h 涨跌: ${ticker.percentage?.toFixed(2)}%`);
            console.log(`📊 24h 成交量: ${ticker.baseVolume?.toFixed(2)}`);
            
            // 获取订单簿
            const orderbook = await this.exchange.fetchOrderBook(symbol, 5);
            console.log(`\n📋 订单簿 (前5档):`);
            console.log(`买盘:`);
            orderbook.bids.slice(0, 3).forEach((bid, i) => {
                console.log(`   ${i + 1}. $${bid[0]} - ${bid[1]}`);
            });
            console.log(`卖盘:`);
            orderbook.asks.slice(0, 3).forEach((ask, i) => {
                console.log(`   ${i + 1}. $${ask[0]} - ${ask[1]}`);
            });
            
            return { ticker, orderbook };
            
        } catch (error) {
            console.error(`❌ 获取市场数据失败:`, error.message);
            throw error;
        }
    }

    async fetchAccountInfo() {
        try {
            if (!this.exchange.apiKey || this.exchange.apiKey === 'demo_key') {
                console.log(`⚠️  需要 API 密钥才能获取账户信息`);
                return null;
            }

            console.log(`\n👤 获取账户信息...`);
            
            const balance = await this.exchange.fetchBalance();
            console.log(`💰 账户余额:`);
            
            // 显示非零余额
            Object.keys(balance.total).forEach(currency => {
                const amount = balance.total[currency];
                if (amount > 0) {
                    console.log(`   ${currency}: ${amount}`);
                }
            });
            
            return balance;
            
        } catch (error) {
            console.error(`❌ 获取账户信息失败:`, error.message);
            throw error;
        }
    }

    async demonstrateOrderOperations(symbol = 'BTC/USDT') {
        try {
            if (!this.exchange.apiKey || this.exchange.apiKey === 'demo_key') {
                console.log(`\n⚠️  演示模式：订单操作需要真实 API 密钥`);
                console.log(`📝 订单操作示例代码:`);
                console.log(`   // 创建限价买单`);
                console.log(`   const order = await exchange.createLimitBuyOrder('BTC/USDT', 0.001, 50000);`);
                console.log(`   // 创建限价卖单`);
                console.log(`   const order = await exchange.createLimitSellOrder('BTC/USDT', 0.001, 60000);`);
                console.log(`   // 取消订单`);
                console.log(`   await exchange.cancelOrder(order.id, 'BTC/USDT');`);
                return;
            }

            console.log(`\n📝 订单操作演示 (${symbol})...`);
            console.log(`⚠️  注意：这是真实交易环境，请谨慎操作！`);
            
            // 获取当前价格
            const ticker = await this.exchange.fetchTicker(symbol);
            const currentPrice = ticker.last;
            
            // 演示创建订单（但不实际执行）
            console.log(`💡 订单示例（未实际执行）:`);
            console.log(`   限价买单: 价格 $${(currentPrice * 0.95).toFixed(2)}, 数量 0.001`);
            console.log(`   限价卖单: 价格 $${(currentPrice * 1.05).toFixed(2)}, 数量 0.001`);
            
            // 获取历史订单
            const orders = await this.exchange.fetchOrders(symbol, undefined, 10);
            console.log(`📋 最近 ${orders.length} 个订单:`);
            orders.slice(0, 5).forEach(order => {
                console.log(`   ${order.side} ${order.amount} ${order.symbol} @ $${order.price} - ${order.status}`);
            });
            
        } catch (error) {
            console.error(`❌ 订单操作演示失败:`, error.message);
        }
    }
}

async function main() {
    console.log('🚀 CCXT 生产级交易示例启动\n');
    
    try {
        // 选择交易所
        const exchangeName = 'binance';
        const exchangeConfig = config[exchangeName];
        
        if (!exchangeConfig) {
            throw new Error(`未找到 ${exchangeName} 的配置`);
        }
        
        // 创建交易机器人
        const bot = new TradingBot(exchangeName, exchangeConfig);
        
        // 初始化
        await bot.initialize();
        
        // 获取市场数据
        await bot.fetchMarketData('BTC/USDT');
        await bot.fetchMarketData('ETH/USDT');
        
        // 获取账户信息
        await bot.fetchAccountInfo();
        
        // 演示订单操作
        await bot.demonstrateOrderOperations('BTC/USDT');
        
        console.log('\n✅ 示例执行完成');
        console.log('\n📚 下一步建议:');
        console.log('1. 配置真实的 API 密钥');
        console.log('2. 在沙盒环境中测试');
        console.log('3. 实现风险管理策略');
        console.log('4. 添加日志和监控');
        console.log('5. 阅读交易所 API 文档');
        
    } catch (error) {
        console.error('❌ 程序执行失败:', error.message);
        process.exit(1);
    }
}

// 错误处理
process.on('unhandledRejection', (reason, promise) => {
    console.error('未处理的 Promise 拒绝:', reason);
    process.exit(1);
});

process.on('uncaughtException', (error) => {
    console.error('未捕获的异常:', error);
    process.exit(1);
});

// 运行主程序
if (require.main === module) {
    main();
}
