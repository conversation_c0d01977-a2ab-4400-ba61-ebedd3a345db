# CCXT 环境变量配置示例
# 复制此文件为 .env 并填入您的真实 API 密钥

# Binance API 配置
BINANCE_API_KEY=your_binance_api_key_here
BINANCE_SECRET=your_binance_secret_here

# Coinbase Pro API 配置
COINBASE_API_KEY=your_coinbase_api_key_here
COINBASE_SECRET=your_coinbase_secret_here
COINBASE_PASSPHRASE=your_coinbase_passphrase_here

# OKX API 配置
OKX_API_KEY=your_okx_api_key_here
OKX_SECRET=your_okx_secret_here
OKX_PASSPHRASE=your_okx_passphrase_here

# Kraken API 配置
KRAKEN_API_KEY=your_kraken_api_key_here
KRAKEN_SECRET=your_kraken_secret_here

# Huobi API 配置
HUOBI_API_KEY=your_huobi_api_key_here
HUOBI_SECRET=your_huobi_secret_here

# Bybit API 配置
BYBIT_API_KEY=your_bybit_api_key_here
BYBIT_SECRET=your_bybit_secret_here

# 通用配置
CCXT_SANDBOX=true
CCXT_VERBOSE=false
CCXT_TIMEOUT=30000
CCXT_RATE_LIMIT=1000

# 注意：
# 1. 将此文件复制为 .env
# 2. 填入您的真实 API 密钥
# 3. 确保 .env 文件在 .gitignore 中
# 4. 不要将 .env 文件提交到版本控制系统
