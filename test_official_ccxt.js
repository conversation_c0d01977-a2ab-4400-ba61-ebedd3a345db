#!/usr/bin/env node

// 测试官方 CCXT 库功能
const ccxt = require('ccxt');

async function testOfficialCCXT() {
    console.log('🚀 测试官方 CCXT 库功能\n');
    
    try {
        // 1. 基本信息
        console.log('📊 基本信息:');
        console.log(`   版本: ${ccxt.version}`);
        console.log(`   支持的交易所: ${ccxt.exchanges.length} 个`);
        console.log(`   热门交易所: ${ccxt.exchanges.slice(0, 10).join(', ')}\n`);
        
        // 2. 创建交易所实例（演示模式）
        console.log('🏢 创建交易所实例:');
        const binance = new ccxt.binance({
            sandbox: true,
            enableRateLimit: true,
        });
        console.log(`   ✅ ${binance.name} (${binance.id}) 创建成功`);
        console.log(`   🌍 国家: ${binance.countries.join(', ')}`);
        console.log(`   ⏱️  速率限制: ${binance.rateLimit}ms\n`);
        
        // 3. 测试公共 API（获取市场数据）
        console.log('📈 测试公共 API:');
        try {
            const ticker = await binance.fetchTicker('BTC/USDT');
            console.log(`   💰 BTC/USDT 价格: $${ticker.last}`);
            console.log(`   📊 24h 变化: ${ticker.percentage?.toFixed(2)}%`);
            console.log(`   📈 24h 最高: $${ticker.high}`);
            console.log(`   📉 24h 最低: $${ticker.low}`);
            console.log(`   💹 24h 成交量: ${ticker.baseVolume?.toFixed(2)} BTC\n`);
        } catch (error) {
            console.log(`   ❌ 获取行情失败: ${error.message}\n`);
        }
        
        // 4. 测试订单簿
        console.log('📋 测试订单簿:');
        try {
            const orderbook = await binance.fetchOrderBook('BTC/USDT', 5);
            console.log('   买盘 (Bids):');
            orderbook.bids.slice(0, 3).forEach((bid, i) => {
                console.log(`     ${i + 1}. $${bid[0]} - ${bid[1]} BTC`);
            });
            console.log('   卖盘 (Asks):');
            orderbook.asks.slice(0, 3).forEach((ask, i) => {
                console.log(`     ${i + 1}. $${ask[0]} - ${ask[1]} BTC`);
            });
            console.log('');
        } catch (error) {
            console.log(`   ❌ 获取订单簿失败: ${error.message}\n`);
        }
        
        // 5. 测试多个交易所
        console.log('🌐 测试多个交易所:');
        const exchanges = ['binance', 'coinbase', 'kraken'];
        
        for (const exchangeName of exchanges) {
            try {
                const exchange = new ccxt[exchangeName]({
                    sandbox: true,
                    enableRateLimit: true,
                });
                
                const ticker = await exchange.fetchTicker('BTC/USDT');
                console.log(`   ${exchange.name}: $${ticker.last}`);
                
            } catch (error) {
                console.log(`   ${exchangeName}: ❌ ${error.message}`);
            }
        }
        console.log('');
        
        // 6. 展示交易所功能
        console.log('🔧 交易所功能支持:');
        const features = [
            'fetchTicker', 'fetchTickers', 'fetchOrderBook', 
            'fetchTrades', 'fetchOHLCV', 'fetchBalance',
            'createOrder', 'cancelOrder', 'fetchOrder'
        ];
        
        features.forEach(feature => {
            const supported = binance.has[feature] ? '✅' : '❌';
            console.log(`   ${supported} ${feature}`);
        });
        console.log('');
        
        // 7. API 密钥配置提示
        console.log('🔑 API 密钥配置:');
        console.log('   要使用私有 API 功能（如获取余额、下单），需要配置 API 密钥:');
        console.log('   ```javascript');
        console.log('   const exchange = new ccxt.binance({');
        console.log('       apiKey: "your_api_key",');
        console.log('       secret: "your_secret",');
        console.log('       sandbox: true, // 测试环境');
        console.log('   });');
        console.log('   ```\n');
        
        console.log('✅ 官方 CCXT 库测试完成！');
        console.log('\n📚 下一步:');
        console.log('1. 获取交易所 API 密钥');
        console.log('2. 配置 config.js 文件');
        console.log('3. 运行生产级示例');
        console.log('4. 阅读 SECURITY_GUIDE.md');
        
    } catch (error) {
        console.error('❌ 测试失败:', error.message);
        process.exit(1);
    }
}

// 运行测试
if (require.main === module) {
    testOfficialCCXT();
}
