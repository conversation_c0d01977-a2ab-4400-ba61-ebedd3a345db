# -*- coding: utf-8 -*-

"""
CCXT Base Exchange Class
"""

import asyncio
import time
from typing import Dict, List, Optional, Any
from .errors import *

class Exchange:
    """Base exchange class"""
    
    def __init__(self, config: Dict[str, Any] = {}):
        """Initialize exchange"""
        self.id = 'base'
        self.name = 'Base Exchange'
        self.countries = []
        self.rateLimit = 2000
        self.certified = False
        self.pro = False
        self.has = {
            'fetchTicker': False,
            'fetchTickers': False,
            'fetchOrderBook': False,
            'fetchTrades': False,
            'fetchOHLCV': False,
            'fetchBalance': False,
            'createOrder': False,
            'cancelOrder': False,
            'fetchOrder': False,
            'fetchOrders': False,
            'fetchOpenOrders': False,
            'fetchClosedOrders': False,
        }
        
        # Configuration
        self.apiKey = config.get('apiKey', '')
        self.secret = config.get('secret', '')
        self.password = config.get('password', '')
        self.sandbox = config.get('sandbox', False)
        self.verbose = config.get('verbose', False)
        self.timeout = config.get('timeout', 10000)
        
        # URLs
        self.urls = {
            'api': {},
            'www': '',
            'doc': [],
            'fees': '',
        }
        
        # Markets and symbols
        self.markets = {}
        self.symbols = []
        self.currencies = {}
        
        # Last request timestamp for rate limiting
        self.lastRestRequestTimestamp = 0
        
    def log(self, *args):
        """Log message if verbose mode is enabled"""
        if self.verbose:
            print(f"[{self.id}]", *args)
    
    def milliseconds(self) -> int:
        """Get current timestamp in milliseconds"""
        return int(time.time() * 1000)
    
    def sleep(self, milliseconds: int):
        """Sleep for specified milliseconds"""
        time.sleep(milliseconds / 1000)
    
    async def fetch_ticker(self, symbol: str, params: Dict = {}) -> Dict:
        """Fetch ticker for a symbol"""
        raise NotSupported(f"{self.id} does not support fetchTicker")
    
    async def fetch_tickers(self, symbols: List[str] = None, params: Dict = {}) -> Dict:
        """Fetch tickers for multiple symbols"""
        raise NotSupported(f"{self.id} does not support fetchTickers")
    
    async def fetch_order_book(self, symbol: str, limit: int = None, params: Dict = {}) -> Dict:
        """Fetch order book for a symbol"""
        raise NotSupported(f"{self.id} does not support fetchOrderBook")
    
    async def fetch_trades(self, symbol: str, since: int = None, limit: int = None, params: Dict = {}) -> List:
        """Fetch trades for a symbol"""
        raise NotSupported(f"{self.id} does not support fetchTrades")
    
    async def fetch_ohlcv(self, symbol: str, timeframe: str = '1m', since: int = None, limit: int = None, params: Dict = {}) -> List:
        """Fetch OHLCV data for a symbol"""
        raise NotSupported(f"{self.id} does not support fetchOHLCV")
    
    async def fetch_balance(self, params: Dict = {}) -> Dict:
        """Fetch account balance"""
        raise NotSupported(f"{self.id} does not support fetchBalance")
    
    async def create_order(self, symbol: str, type: str, side: str, amount: float, price: float = None, params: Dict = {}) -> Dict:
        """Create an order"""
        raise NotSupported(f"{self.id} does not support createOrder")
    
    async def cancel_order(self, id: str, symbol: str = None, params: Dict = {}) -> Dict:
        """Cancel an order"""
        raise NotSupported(f"{self.id} does not support cancelOrder")
    
    async def fetch_order(self, id: str, symbol: str = None, params: Dict = {}) -> Dict:
        """Fetch order by ID"""
        raise NotSupported(f"{self.id} does not support fetchOrder")
    
    async def fetch_orders(self, symbol: str = None, since: int = None, limit: int = None, params: Dict = {}) -> List:
        """Fetch orders"""
        raise NotSupported(f"{self.id} does not support fetchOrders")
    
    async def fetch_open_orders(self, symbol: str = None, since: int = None, limit: int = None, params: Dict = {}) -> List:
        """Fetch open orders"""
        raise NotSupported(f"{self.id} does not support fetchOpenOrders")
    
    async def fetch_closed_orders(self, symbol: str = None, since: int = None, limit: int = None, params: Dict = {}) -> List:
        """Fetch closed orders"""
        raise NotSupported(f"{self.id} does not support fetchClosedOrders")
    
    def __str__(self):
        return f"{self.name} ({self.id})"
    
    def __repr__(self):
        return f"<{self.__class__.__name__} {self.id}>"
