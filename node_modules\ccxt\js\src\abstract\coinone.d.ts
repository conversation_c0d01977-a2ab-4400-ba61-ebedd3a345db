import { implicitReturnType } from '../base/types.js';
import { Exchange as _Exchange } from '../base/Exchange.js';
interface Exchange {
    publicGetOrderbook(params?: {}): Promise<implicitReturnType>;
    publicGetTicker(params?: {}): Promise<implicitReturnType>;
    publicGetTickerUtc(params?: {}): Promise<implicitReturnType>;
    publicGetTrades(params?: {}): Promise<implicitReturnType>;
    v2PublicGetRangeUnits(params?: {}): Promise<implicitReturnType>;
    v2PublicGetMarketsQuoteCurrency(params?: {}): Promise<implicitReturnType>;
    v2PublicGetMarketsQuoteCurrencyTargetCurrency(params?: {}): Promise<implicitReturnType>;
    v2PublicGetOrderbookQuoteCurrencyTargetCurrency(params?: {}): Promise<implicitReturnType>;
    v2PublicGetTradesQuoteCurrencyTargetCurrency(params?: {}): Promise<implicitReturnType>;
    v2PublicGetTickerNewQuoteCurrency(params?: {}): Promise<implicitReturnType>;
    v2PublicGetTickerNewQuoteCurrencyTargetCurrency(params?: {}): Promise<implicitReturnType>;
    v2PublicGetTickerUtcNewQuoteCurrency(params?: {}): Promise<implicitReturnType>;
    v2PublicGetTickerUtcNewQuoteCurrencyTargetCurrency(params?: {}): Promise<implicitReturnType>;
    v2PublicGetCurrencies(params?: {}): Promise<implicitReturnType>;
    v2PublicGetCurrenciesCurrency(params?: {}): Promise<implicitReturnType>;
    v2PublicGetChartQuoteCurrencyTargetCurrency(params?: {}): Promise<implicitReturnType>;
    privatePostAccountDepositAddress(params?: {}): Promise<implicitReturnType>;
    privatePostAccountBtcDepositAddress(params?: {}): Promise<implicitReturnType>;
    privatePostAccountBalance(params?: {}): Promise<implicitReturnType>;
    privatePostAccountDailyBalance(params?: {}): Promise<implicitReturnType>;
    privatePostAccountUserInfo(params?: {}): Promise<implicitReturnType>;
    privatePostAccountVirtualAccount(params?: {}): Promise<implicitReturnType>;
    privatePostOrderCancelAll(params?: {}): Promise<implicitReturnType>;
    privatePostOrderCancel(params?: {}): Promise<implicitReturnType>;
    privatePostOrderLimitBuy(params?: {}): Promise<implicitReturnType>;
    privatePostOrderLimitSell(params?: {}): Promise<implicitReturnType>;
    privatePostOrderCompleteOrders(params?: {}): Promise<implicitReturnType>;
    privatePostOrderLimitOrders(params?: {}): Promise<implicitReturnType>;
    privatePostOrderOrderInfo(params?: {}): Promise<implicitReturnType>;
    privatePostTransactionAuthNumber(params?: {}): Promise<implicitReturnType>;
    privatePostTransactionHistory(params?: {}): Promise<implicitReturnType>;
    privatePostTransactionKrwHistory(params?: {}): Promise<implicitReturnType>;
    privatePostTransactionBtc(params?: {}): Promise<implicitReturnType>;
    privatePostTransactionCoin(params?: {}): Promise<implicitReturnType>;
    v2PrivatePostAccountBalance(params?: {}): Promise<implicitReturnType>;
    v2PrivatePostAccountDepositAddress(params?: {}): Promise<implicitReturnType>;
    v2PrivatePostAccountUserInfo(params?: {}): Promise<implicitReturnType>;
    v2PrivatePostAccountVirtualAccount(params?: {}): Promise<implicitReturnType>;
    v2PrivatePostOrderCancel(params?: {}): Promise<implicitReturnType>;
    v2PrivatePostOrderLimitBuy(params?: {}): Promise<implicitReturnType>;
    v2PrivatePostOrderLimitSell(params?: {}): Promise<implicitReturnType>;
    v2PrivatePostOrderLimitOrders(params?: {}): Promise<implicitReturnType>;
    v2PrivatePostOrderCompleteOrders(params?: {}): Promise<implicitReturnType>;
    v2PrivatePostOrderQueryOrder(params?: {}): Promise<implicitReturnType>;
    v2PrivatePostTransactionAuthNumber(params?: {}): Promise<implicitReturnType>;
    v2PrivatePostTransactionBtc(params?: {}): Promise<implicitReturnType>;
    v2PrivatePostTransactionHistory(params?: {}): Promise<implicitReturnType>;
    v2PrivatePostTransactionKrwHistory(params?: {}): Promise<implicitReturnType>;
    v2_1PrivatePostAccountBalanceAll(params?: {}): Promise<implicitReturnType>;
    v2_1PrivatePostAccountBalance(params?: {}): Promise<implicitReturnType>;
    v2_1PrivatePostAccountTradeFee(params?: {}): Promise<implicitReturnType>;
    v2_1PrivatePostAccountTradeFeeQuoteCurrencyTargetCurrency(params?: {}): Promise<implicitReturnType>;
    v2_1PrivatePostOrderLimit(params?: {}): Promise<implicitReturnType>;
    v2_1PrivatePostOrderCancel(params?: {}): Promise<implicitReturnType>;
    v2_1PrivatePostOrderCancelAll(params?: {}): Promise<implicitReturnType>;
    v2_1PrivatePostOrderOpenOrders(params?: {}): Promise<implicitReturnType>;
    v2_1PrivatePostOrderOpenOrdersAll(params?: {}): Promise<implicitReturnType>;
    v2_1PrivatePostOrderCompleteOrders(params?: {}): Promise<implicitReturnType>;
    v2_1PrivatePostOrderCompleteOrdersAll(params?: {}): Promise<implicitReturnType>;
    v2_1PrivatePostOrderInfo(params?: {}): Promise<implicitReturnType>;
    v2_1PrivatePostTransactionKrwHistory(params?: {}): Promise<implicitReturnType>;
    v2_1PrivatePostTransactionCoinHistory(params?: {}): Promise<implicitReturnType>;
    v2_1PrivatePostTransactionCoinWithdrawalLimit(params?: {}): Promise<implicitReturnType>;
}
declare abstract class Exchange extends _Exchange {
}
export default Exchange;
