#!/usr/bin/env node

// CCXT Deployment Verification Script
const fs = require('fs');
const path = require('path');

console.log('=== CCXT Deployment Verification ===\n');

// Test 1: Check if CCXT library can be loaded
console.log('1. Testing CCXT library loading...');
try {
    const ccxt = require('./ccxt');
    console.log('✓ CCXT library loaded successfully');
    console.log(`  Version: ${ccxt.version}`);
} catch (error) {
    console.log('✗ Failed to load CCXT library:', error.message);
    process.exit(1);
}

// Test 2: Check project structure
console.log('\n2. Checking project structure...');
const requiredDirs = ['js', 'python', 'php', 'examples', 'build', 'dist', 'node_modules'];
const requiredFiles = ['package.json', 'ccxt.js', 'postinstall.js'];

let structureOk = true;

requiredDirs.forEach(dir => {
    if (fs.existsSync(dir)) {
        console.log(`✓ Directory exists: ${dir}`);
    } else {
        console.log(`✗ Missing directory: ${dir}`);
        structureOk = false;
    }
});

requiredFiles.forEach(file => {
    if (fs.existsSync(file)) {
        console.log(`✓ File exists: ${file}`);
    } else {
        console.log(`✗ Missing file: ${file}`);
        structureOk = false;
    }
});

// Test 3: Check Node.js dependencies
console.log('\n3. Checking Node.js dependencies...');
try {
    const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
    const dependencies = Object.keys(packageJson.dependencies || {});
    const devDependencies = Object.keys(packageJson.devDependencies || {});
    
    console.log(`✓ Dependencies: ${dependencies.length} packages`);
    console.log(`✓ Dev Dependencies: ${devDependencies.length} packages`);
    
    // Check if node_modules exists and has packages
    if (fs.existsSync('node_modules')) {
        const nodeModulesCount = fs.readdirSync('node_modules').length;
        console.log(`✓ Node modules installed: ${nodeModulesCount} packages`);
    }
} catch (error) {
    console.log('✗ Error checking dependencies:', error.message);
    structureOk = false;
}

// Test 4: Test basic functionality
console.log('\n4. Testing basic functionality...');
try {
    const ccxt = require('./ccxt');
    
    // Test Exchange class
    const exchange = new ccxt.Exchange();
    console.log('✓ Exchange class instantiation works');
    
    // Test error handling
    try {
        exchange.fetchTicker('BTC/USDT');
        console.log('✗ Expected error was not thrown');
    } catch (error) {
        if (error.message.includes('not implemented')) {
            console.log('✓ Error handling works correctly');
        } else {
            console.log('✗ Unexpected error:', error.message);
        }
    }
    
} catch (error) {
    console.log('✗ Basic functionality test failed:', error.message);
    structureOk = false;
}

// Test 5: Check examples
console.log('\n5. Checking examples...');
const exampleFiles = [
    'examples/js/basic-chart.js',
    'examples/js/basic-usage.js',
    'examples/py/basic_usage.py'
];

exampleFiles.forEach(file => {
    if (fs.existsSync(file)) {
        console.log(`✓ Example exists: ${file}`);
    } else {
        console.log(`✗ Missing example: ${file}`);
    }
});

// Final result
console.log('\n=== Deployment Verification Results ===');
if (structureOk) {
    console.log('✓ CCXT deployment verification PASSED');
    console.log('\nDeployment is ready for use!');
    console.log('\nNext steps:');
    console.log('1. Run "npm start" to test JavaScript examples');
    console.log('2. Run "python examples/py/basic_usage.py" to test Python examples');
    console.log('3. Configure API credentials for real trading');
    console.log('4. Read the documentation at https://docs.ccxt.com');
    console.log('\nNote: This is a minimal implementation for demonstration.');
    console.log('For production use, install the official CCXT library:');
    console.log('npm install ccxt  # for JavaScript');
    console.log('pip install ccxt  # for Python');
} else {
    console.log('✗ CCXT deployment verification FAILED');
    console.log('\nPlease check the errors above and fix them.');
    process.exit(1);
}

console.log('\n=== Verification Complete ===');
