// CCXT API 配置示例文件
// 复制此文件为 config.js 并填入您的真实 API 密钥

module.exports = {
    // Binance 配置
    binance: {
        apiKey: 'your_binance_api_key_here',
        secret: 'your_binance_secret_here',
        sandbox: true, // 设置为 false 用于实盘交易
        enableRateLimit: true,
        options: {
            defaultType: 'spot', // 'spot', 'margin', 'future', 'delivery'
        }
    },

    // Coinbase Pro 配置
    coinbasepro: {
        apiKey: 'your_coinbase_api_key_here',
        secret: 'your_coinbase_secret_here',
        password: 'your_coinbase_passphrase_here',
        sandbox: true,
        enableRateLimit: true,
    },

    // OKX 配置
    okx: {
        apiKey: 'your_okx_api_key_here',
        secret: 'your_okx_secret_here',
        password: 'your_okx_passphrase_here',
        sandbox: true,
        enableRateLimit: true,
    },

    // Kraken 配置
    kraken: {
        apiKey: 'your_kraken_api_key_here',
        secret: 'your_kraken_secret_here',
        enableRateLimit: true,
    },

    // Huobi 配置
    huobi: {
        apiKey: 'your_huobi_api_key_here',
        secret: 'your_huobi_secret_here',
        enableRateLimit: true,
    },

    // 通用配置
    common: {
        timeout: 30000, // 30秒超时
        verbose: false, // 设置为 true 启用详细日志
        enableRateLimit: true,
        rateLimit: 1000, // 毫秒
    }
};

// 安全提示：
// 1. 永远不要将真实的 API 密钥提交到版本控制系统
// 2. 使用环境变量存储敏感信息
// 3. 在生产环境中设置 sandbox: false
// 4. 定期轮换 API 密钥
// 5. 为 API 密钥设置适当的权限（只读、交易等）
