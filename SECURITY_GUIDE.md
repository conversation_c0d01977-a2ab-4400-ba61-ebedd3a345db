# CCXT 安全配置指南

## 🔐 API 密钥安全

### 1. 获取 API 密钥

#### Binance
1. 登录 Binance 账户
2. 进入 "API 管理" 页面
3. 创建新的 API 密钥
4. **重要：** 设置 IP 白名单
5. **重要：** 只启用必要的权限（读取、交易等）

#### Coinbase Pro
1. 登录 Coinbase Pro
2. 进入 "API" 设置
3. 创建新的 API 密钥
4. 记录 API Key, Secret, 和 Passphrase

#### OKX
1. 登录 OKX 账户
2. 进入 "API" 管理
3. 创建 API 密钥
4. 设置 IP 白名单和权限

### 2. 安全存储 API 密钥

#### ✅ 推荐方式：环境变量
```bash
# Linux/Mac
export BINANCE_API_KEY="your_api_key"
export BINANCE_SECRET="your_secret"

# Windows
set BINANCE_API_KEY=your_api_key
set BINANCE_SECRET=your_secret
```

#### ✅ 推荐方式：.env 文件
```bash
# .env 文件
BINANCE_API_KEY=your_api_key
BINANCE_SECRET=your_secret
```

#### ❌ 不推荐：硬编码
```javascript
// 不要这样做！
const apiKey = "your_actual_api_key"; // 危险！
```

### 3. .gitignore 配置

确保敏感文件不被提交到版本控制：

```gitignore
# API 配置文件
config.js
config.py
.env
.env.local
.env.production

# 日志文件
*.log
logs/

# 临时文件
*.tmp
*.temp
```

## 🛡️ 安全最佳实践

### 1. API 权限设置

- **只读权限：** 用于获取市场数据和账户信息
- **交易权限：** 仅在需要时启用
- **提现权限：** 通常不需要，建议禁用

### 2. IP 白名单

```bash
# 设置固定 IP 地址
# 例如：*************
# 或使用 VPS 的固定 IP
```

### 3. 沙盒环境测试

```javascript
// 始终先在沙盒环境测试
const exchange = new ccxt.binance({
    apiKey: 'your_key',
    secret: 'your_secret',
    sandbox: true, // 重要：测试环境
});
```

### 4. 错误处理和日志

```javascript
try {
    const result = await exchange.fetchBalance();
} catch (error) {
    if (error instanceof ccxt.AuthenticationError) {
        console.log('认证失败，检查 API 密钥');
    } else if (error instanceof ccxt.NetworkError) {
        console.log('网络错误，稍后重试');
    }
    // 记录错误但不暴露敏感信息
    console.log('操作失败:', error.message);
}
```

## 🔄 API 密钥轮换

### 定期更换 API 密钥

1. **频率：** 建议每 30-90 天更换一次
2. **步骤：**
   - 创建新的 API 密钥
   - 更新应用程序配置
   - 测试新密钥
   - 删除旧密钥

### 密钥泄露应急处理

1. **立即禁用** 泄露的 API 密钥
2. **检查** 账户活动记录
3. **更改** 账户密码
4. **启用** 双因素认证
5. **创建** 新的 API 密钥

## 📊 监控和告警

### 1. 账户监控

```javascript
// 定期检查账户余额变化
async function monitorAccount() {
    const balance = await exchange.fetchBalance();
    // 检查异常变化
    // 发送告警通知
}
```

### 2. 交易监控

```javascript
// 监控交易活动
async function monitorTrades() {
    const trades = await exchange.fetchMyTrades();
    // 检查异常交易
    // 记录交易日志
}
```

## 🚨 风险管理

### 1. 资金管理

- **分散资金：** 不要将所有资金放在一个交易所
- **限制额度：** 设置单笔交易和日交易限额
- **止损策略：** 实施自动止损机制

### 2. 技术风险

```javascript
// 实施重试机制
async function safeApiCall(apiFunction, maxRetries = 3) {
    for (let i = 0; i < maxRetries; i++) {
        try {
            return await apiFunction();
        } catch (error) {
            if (i === maxRetries - 1) throw error;
            await sleep(1000 * (i + 1)); // 指数退避
        }
    }
}
```

### 3. 网络安全

- **使用 HTTPS：** 确保所有 API 调用使用 HTTPS
- **VPN：** 考虑使用 VPN 保护网络连接
- **防火墙：** 配置适当的防火墙规则

## 📋 安全检查清单

### 部署前检查

- [ ] API 密钥存储在环境变量中
- [ ] 敏感文件已添加到 .gitignore
- [ ] 启用了 IP 白名单
- [ ] 设置了适当的 API 权限
- [ ] 在沙盒环境中测试
- [ ] 实施了错误处理
- [ ] 配置了日志记录
- [ ] 设置了监控告警

### 运行时检查

- [ ] 定期检查账户活动
- [ ] 监控 API 调用频率
- [ ] 检查网络连接安全
- [ ] 验证交易记录
- [ ] 备份重要数据

## 🆘 紧急联系方式

### 交易所客服

- **Binance：** https://www.binance.com/en/support
- **Coinbase：** https://help.coinbase.com/
- **OKX：** https://www.okx.com/support

### 安全事件报告

如果发现安全问题：

1. **立即停止** 所有交易活动
2. **禁用** API 密钥
3. **联系** 交易所客服
4. **保存** 相关证据
5. **报告** 安全事件

## 📚 延伸阅读

- [CCXT 官方文档](https://docs.ccxt.com)
- [加密货币安全最佳实践](https://academy.binance.com/en/articles/cryptocurrency-security-tips)
- [API 安全指南](https://owasp.org/www-project-api-security/)

---

**记住：安全是第一位的！在处理真实资金时，永远要谨慎行事。**
